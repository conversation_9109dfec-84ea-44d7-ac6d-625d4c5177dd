package main

import (
	"flag"
	"fmt"
	"sync"
	"time"

	"github.com/precize/analyzer/config"
	"github.com/precize/analyzer/internal/elasticsearch"
	"github.com/precize/analyzer/internal/esjson"
	"github.com/precize/analyzer/internal/services"
	"github.com/precize/analyzer/internal/utils"
	"github.com/precize/analyzer/pkg/cron"
	"github.com/precize/analyzer/pkg/kv"
	"github.com/precize/email"
	"github.com/precize/logger"
)

const (
	BATCH_SIZE               = 10                        // BATCH_SIZE is the number of tenants to process in a batch
	DEFAULT_CONFIG_FILE_PATH = "./local-application.yml" // CONFIG_FILE is the path to the configuration file
)

var interval time.Duration // The fetch interval

func main() {
	defer func() {
		if r := recover(); r != nil {
			logger.Print(logger.ERROR, "Panic occured", r)
			email.SendPanicEmail("analyzer")
		}
	}()

	configFilePath := flag.String("config", "application.yml", "Path to application.yml")
	debug := flag.Bool("debug", false, "Debug mode")
	flag.Parse()

	if configFilePath == nil || *configFilePath == "" {
		*configFilePath = DEFAULT_CONFIG_FILE_PATH
	}

	// Initialize the logger
	logger.InitializeLogs("analyzer", *debug)

	// Load the configuration
	if err := config.LoadConfig(configFilePath); err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Error loading config: %s", err))
		return
	}

	// Watch the configuration file for changes
	go config.WatchConfigFile(*configFilePath, func() {
		interval = config.GetFetchInterval()
	})

	// Create a new badger db instance
	kv, err := kv.NewBadgerDB()
	if err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Error creating badger db: %s", err))
		return
	}
	defer kv.Close()

	// Create a new elasticsearch client
	es := elasticsearch.NewClient()

	if _, err := es.Ping(); err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Error pinging elasticsearch: %s", err))
		return
	}

	// Setup mappings
	mappings := map[string]esjson.MappingType{
		elasticsearch.EVENTS_STORE_INDEX:     esjson.EVENTS_STORE_INDEX,
		elasticsearch.ENTITY_BEHAVIOUR_INDEX: esjson.ENTITY_BEHAVIOUR_INDEX,
		elasticsearch.ANOMALIES_INDEX:        esjson.ANOMALIES_INDEX,
	}

	// Ensure the events_store index exists
	if err := es.EnsureIndicesExist(mappings); err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Error ensuring events index exists: %s", err))
		return
	}

	// Get the worker interval from the configuration
	interval = config.GetFetchInterval()

	logger.Print(logger.INFO, fmt.Sprintf("Starting worker with interval: %s", interval))

	// Fetch all tenant ids
	tenants, err := es.FetchTenantIds()
	if err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Error fetching tenant ids: %s", err))
		return
	}

	err = loadExistingEntityBehaviours(es, tenants)
	if err != nil {
		logger.Print(logger.ERROR, fmt.Sprintf("Error loading existing entity behaviours: %s", err))
		return
	}

	// Start the cron scheduler
	scheduler := cron.New()

	// Define the function to refresh entity behaviour
	refreshEntityBehaviour := func() {
		logger.Print(logger.INFO, "Aggregating entity behaviour for the past 3 months…")

		now := time.Now().UTC()
		start := time.Date(now.Year(), now.Month()-2, 1, 0, 0, 0, 0, time.UTC) // 1st day of 3 months ago
		end := now

		for _, tenantID := range tenants {
			entityBehaviours, err := es.AggregateEntityBehaviourWithTimestamp(tenantID, start, end)
			if err != nil {
				logger.Print(logger.ERROR,
					fmt.Sprintf("Error aggregating entity behaviour for tenant %s: %s", tenantID, err))
				continue
			}

			if len(entityBehaviours) > 0 {
				if err := es.StoreEntityBehaviour(entityBehaviours); err != nil {
					logger.Print(logger.ERROR,
						fmt.Sprintf("Error storing entity behaviour for tenant %s: %s", tenantID, err))
				}
			}
		}
	}

	refreshJob := &cron.CronJob{
		CronScheduler: scheduler,
		Expression:    "59 23 * * *", // 23:59 everyday.
		ExecuteFunc: func() {
			now := time.Now()
			tomorrow := now.AddDate(0, 0, 1)
			if tomorrow.Day() == 1 {
				refreshEntityBehaviour()
			}
		},
	}

	cron.RegisterCronJob(refreshJob)

	// Start the cron scheduler
	logger.Print(logger.INFO, "Starting the cron scheduler...")
	scheduler.Start()

	// Start the worker
	for {
		start := time.Now()

		logger.Print(logger.INFO, "Starting new fetch cycle")

		// Start Processing 10 tenants at a time
		tenantCount := len(tenants)

		for batchStart := 0; batchStart < tenantCount; batchStart += BATCH_SIZE {
			batchEnd := batchStart + BATCH_SIZE
			if batchEnd > tenantCount {
				batchEnd = tenantCount
			}
			batch := tenants[batchStart:batchEnd]

			var wg sync.WaitGroup
			wg.Add(len(batch))

			for _, tenantID := range batch {
				go func(tid string) {
					defer wg.Done()

					// Get the last event time for this tenant
					// which would be the eventTime of the latest event
					// in the previous interval fetch else the default
					// last event time from the configuration
					lastEventTimeKey := fmt.Sprintf("last_event_time_%s", tid)
					lastEventTime, err := kv.Get([]byte(lastEventTimeKey))
					if err != nil {
						logger.Print(logger.INFO, fmt.Sprintf("Error getting last_event_time for tenant %s: %s", tid, err))
						return
					}

					if lastEventTime == "" {
						lastEventTime = time.Now().Add(-60 * time.Minute).UTC().Format(utils.ESTimeLayout)
					}

					// Fetch the aggregated data for this tenant from lastEventTime to now
					events, newLastEventTime, err := es.FetchAggregatedData(tid, lastEventTime)
					if err != nil {
						logger.Print(logger.INFO, fmt.Sprintf("Error fetching aggregated data for tenant %s: %s", tid, err))
						return
					}

					// If no new events are fetched, return
					if len(events) == 0 {
						logger.Print(logger.INFO, fmt.Sprintf("No new events fetched for tenant %s.", tid))
						return
					}

					// Anomaly Channel
					anomalyChan := make(chan elasticsearch.Anomaly, 1000)
					defer close(anomalyChan)

					// Start anomaly processor
					wg.Add(1)
					go func() {
						defer wg.Done()
						services.ProcessAnomalies(es, anomalyChan, nil, nil, nil)
					}()

					logger.Print(logger.INFO, fmt.Sprintf("Fetched %d events for tenant %s", len(events), tid))

					ids := make([]string, len(events))
					for i, event := range events {
						ids[i] = event.ID
					}

					// Check which event_ids already exist in the events_store
					// index and get a list of found existing events and a list of
					// not found event_ids
					foundDocs, notFoundIDs, err := es.GetExistingEvents(elasticsearch.EVENTS_STORE_INDEX, ids)
					if err != nil {
						logger.Print(logger.INFO, fmt.Sprintf("Error checking document existence for tenant %s: %s", tid, err))
						return
					}

					bulkBody, existingEvents := elasticsearch.PrepareMergedEvents(elasticsearch.EVENTS_STORE_INDEX, events, foundDocs, notFoundIDs)

					// Detect Anomalies
					entityBehaviours := elasticsearch.GetEntityBehaviour(tid)
					if len(entityBehaviours) > 0 && entityBehaviours != nil {
						_events := make([]elasticsearch.Event, 0)

						// Convert notFoundIDs []string to map[string]struct{} for faster lookup
						notFoundIDsMap := make(map[string]struct{}, len(notFoundIDs))
						for _, id := range notFoundIDs {
							notFoundIDsMap[id] = struct{}{}
						}

						for _, event := range events {
							if _, ok := notFoundIDsMap[event.ID]; ok {
								_events = append(_events, event)
							}
						}

						// Add existing events
						for _, event := range existingEvents {
							_events = append(_events, event)
						}

						for _, event := range _events {
							var username string
							accessKey, isAccessKey := event.Additional["accessKeyId"]
							if isAccessKey {
								username = accessKey.(string)
							} else {
								username = event.Username
							}
							for _, behaviour := range entityBehaviours {
								if behaviour.EntityName == username &&
									behaviour.TenantID == event.TenantID {
									// Get anomalies for this event
									eventAnomalies := services.DetectAnomalies(*behaviour, event)

									// Send anomalies to processor
									for anomaly := range eventAnomalies {
										anomalyChan <- anomaly
									}
								}
							}
						}
					}

					// Bulk upsert the new events
					// If the event_id is found in the existing events, update the existing event
					// else insert the new event
					err = es.BulkUpsertEvents(elasticsearch.EVENTS_STORE_INDEX, bulkBody)
					if err != nil {
						logger.Print(logger.INFO, fmt.Sprintf("Error bulk upserting events for tenant %s: %s", tid, err))
						return
					}

					// Update the last event time for this tenant
					err = kv.Set([]byte(lastEventTimeKey), newLastEventTime)
					if err != nil {
						logger.Print(logger.INFO, fmt.Sprintf("Error setting last_event_time for tenant %s: %s", tid, err))
						return
					}

					logger.Print(logger.INFO, fmt.Sprintf("Updated last_event_time for tenant %s to %s", tid, newLastEventTime))
				}(tenantID)
			}
			wg.Wait()
		}

		// Sleep for the remaining time in the interval
		// unless the aggregation cycle took longer than the interval
		elapsed := time.Since(start)
		if elapsed < interval {
			logger.Print(logger.INFO, fmt.Sprintf("Aggregation cycle completed in %s, sleeping for %s", elapsed, interval-elapsed))
			time.Sleep(interval - elapsed)
		}
	}
}

func loadExistingEntityBehaviours(es *elasticsearch.Client, tenants []string) error {
	for _, tenant := range tenants {
		behaviours, err := elasticsearch.FetchLatestEntityBehaviourByTenant(es, tenant)
		if err != nil {
			logger.Print(logger.INFO, fmt.Sprintf("Error loading entity behaviours for tenant %s: %s", tenant, err))
			return err
		}
		if behaviours != nil {
			elasticsearch.SetEntityBehaviour(tenant, behaviours)
		}
	}

	logger.Print(logger.INFO, "Loaded existing entity behaviours")
	return nil
}
