package esjson

import "fmt"

type QueryType string

const (
	EVENTS_AGGREGATION           QueryType = "events_aggregation"
	FETCH_ONLY_DOC_ID            QueryType = "fetch_only_doc_id"
	ENTITY_BEHAVIOUR_AGGREGATION QueryType = "entity_behaviour_aggregation"
	GET_DATA_BY_TENANT_ID        QueryType = "get_data_by_tenant"
	FETCH_ENTITY_BEHAVIOUR       QueryType = "fetch_entity_behaviour"
	LATEST_ENTITY_BEHAVIOUR      QueryType = "latest_entity_behaviour"
)

type QueryParams struct {
	StartTime string
	EndTime   string
	TenantId  string
	Username  string
}

func ValidateParams(params QueryParams) error {
	if params.StartTime == "" {
		return fmt.Errorf("startTime is required")
	}
	if params.EndTime == "" {
		return fmt.Errorf("endTime is required")
	}

	// ...more validations can be added here

	return nil
}
